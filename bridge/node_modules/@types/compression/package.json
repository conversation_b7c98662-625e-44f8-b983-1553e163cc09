{"name": "@types/compression", "version": "1.8.0", "description": "TypeScript definitions for compression", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/compression", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>", "url": "https://github.com/santialbo"}, {"name": "<PERSON>", "githubUsername": "rburgt", "url": "https://github.com/rburgt"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/neil<PERSON><PERSON>son"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/compression"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "bb1feed5bdeb266df44db7cd08d08637cdeae8e640100b1869c8e8a165d87dc5", "typeScriptVersion": "5.1"}