import { KernelConnector } from './kernel-connector';
import { ClientSession } from './server';
interface CommandResult {
    success: boolean;
    output?: string;
    error?: string;
}
export declare class CommandProcessor {
    private kernelConnector;
    private commandHistory;
    constructor(kernelConnector: KernelConnector);
    processCommand(command: string, session: ClientSession): Promise<CommandResult>;
    private handlePs;
    private handleLs;
    private handleCat;
    private handleEcho;
    private handlePwd;
    private handleCd;
    private handleMkdir;
    private handleTouch;
    private handleRm;
    private handleTop;
    private handleNetstat;
    private handleHistory;
    private handleEnv;
    private handleExport;
    private handleKill;
    private handleJobs;
    private handleDf;
    private handleFree;
    private handleUptime;
}
export {};
//# sourceMappingURL=command-processor.d.ts.map