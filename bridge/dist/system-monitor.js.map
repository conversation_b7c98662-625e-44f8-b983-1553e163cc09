{"version": 3, "file": "system-monitor.js", "sourceRoot": "", "sources": ["../src/system-monitor.ts"], "names": [], "mappings": ";;;;;;AAAA,mCAAsC;AACtC,sDAA8B;AAE9B,MAAM,MAAM,GAAG,iBAAO,CAAC,YAAY,CAAC;IAClC,KAAK,EAAE,MAAM;IACb,MAAM,EAAE,iBAAO,CAAC,MAAM,CAAC,OAAO,CAC5B,iBAAO,CAAC,MAAM,CAAC,SAAS,EAAE,EAC1B,iBAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CACtB;IACD,WAAW,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE;IAC1C,UAAU,EAAE;QACV,IAAI,iBAAO,CAAC,UAAU,CAAC,OAAO,CAAC;YAC7B,MAAM,EAAE,iBAAO,CAAC,MAAM,CAAC,MAAM,EAAE;SAChC,CAAC;KACH;CACF,CAAC,CAAC;AAqDH,MAAa,aAAc,SAAQ,qBAAY;IAmB7C;QACE,KAAK,EAAE,CAAC;QAnBF,YAAO,GAAY,KAAK,CAAC;QACzB,oBAAe,GAA0B,IAAI,CAAC;QAC9C,iBAAY,GAAkB,EAAE,CAAC;QACjC,mBAAc,GAAW,GAAG,CAAC;QAC7B,mBAAc,GAAW,IAAI,CAAC;QAG9B,oBAAe,GAAG;YACxB,eAAe,EAAE,GAAG;YACpB,YAAY,EAAE,GAAG;YACjB,cAAc,EAAE,CAAC;YACjB,cAAc,EAAE,CAAC;YACjB,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,CAAC;YACnB,YAAY,EAAE,EAAE;YAChB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;IAIF,CAAC;IAEM,KAAK,CAAC,KAAK;QAChB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACjD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAGvC,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAGxB,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvB,CAAC;IAEM,KAAK,CAAC,IAAI;QACf,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAEvC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvB,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEM,KAAK,CAAC,cAAc;QACzB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACzD,CAAC;IAEM,eAAe;QACpB,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;IAChC,CAAC;IAEO,YAAY;QAClB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;QAGpE,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe;YACtD,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC;YACpC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;QAE/B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY;YAChD,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC;YACpC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAG9B,IAAI,CAAC,eAAe,CAAC,cAAc,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC;QACzE,IAAI,CAAC,eAAe,CAAC,cAAc,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;QACxE,IAAI,CAAC,eAAe,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;QACzE,IAAI,CAAC,eAAe,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;QAGxE,IAAI,CAAC,eAAe,CAAC,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3E,IAAI,CAAC,eAAe,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC;QAElG,MAAM,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;QACvC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;QACvF,MAAM,UAAU,GAAG,WAAW,GAAG,UAAU,CAAC;QAE5C,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;QAEtC,MAAM,KAAK,GAAgB;YACzB,SAAS,EAAE,GAAG,CAAC,WAAW,EAAE;YAC5B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAC1B,MAAM,EAAE;gBACN,KAAK,EAAE,WAAW;gBAClB,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,UAAU;gBAChB,aAAa,EAAE,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,GAAG;gBAC/C,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC;gBACrC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;gBACvC,SAAS,EAAE,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;gBACtD,UAAU,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;gBAC7B,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;aAC7B;YACD,GAAG,EAAE;gBACH,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,GAAG,CAAC,CAAC;gBACzD,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE;oBACR,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;oBAC3C,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;oBAC7C,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;iBAC9C;gBACD,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;gBACnC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;gBACrC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;aACpC;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,YAAY;gBACxC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,GAAG,GAAG,CAAC;gBAC5D,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,GAAG,GAAG,CAAC;gBAC7D,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,GAAG,IAAI,CAAC;gBAC7D,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,GAAG,IAAI,CAAC;aAC7D;YACD,OAAO,EAAE;gBACP,UAAU,EAAE,CAAC;gBACb,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;gBAC/C,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc;gBAC7C,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc;gBAC7C,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB;gBACjD,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB;gBACjD,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBACxC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;aACzC;YACD,UAAU,EAAE;gBACV,WAAW,EAAE,SAAS;gBACtB,UAAU,EAAE,QAAQ;gBACpB,UAAU,EAAE,QAAQ;gBACpB,aAAa,EAAE,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,GAAG;gBAC3C,YAAY,EAAE,MAAM;gBACpB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;gBACrC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;aACtC;SACF,CAAC;QAGF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAG9B,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACnD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC5B,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAE1B,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;YACrC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YACvC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7C,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK;SACjC,CAAC,CAAC;IACL,CAAC;IAGM,aAAa,CAAC,SAAiB,EAAE;QACtC,OAAO,IAAI,CAAC,YAAY;aACrB,KAAK,CAAC,CAAC,MAAM,CAAC;aACd,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa;SAC9B,CAAC,CAAC,CAAC;IACR,CAAC;IAEM,gBAAgB,CAAC,SAAiB,EAAE;QACzC,OAAO,IAAI,CAAC,YAAY;aACrB,KAAK,CAAC,CAAC,MAAM,CAAC;aACd,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;SACjC,CAAC,CAAC,CAAC;IACR,CAAC;IAEM,iBAAiB,CAAC,SAAiB,EAAE;QAK1C,OAAO,IAAI,CAAC,YAAY;aACrB,KAAK,CAAC,CAAC,MAAM,CAAC;aACd,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;YAC/B,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;SAChC,CAAC,CAAC,CAAC;IACR,CAAC;IAGM,cAAc;QAMnB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;QACpD,CAAC;QAED,OAAO;YACL,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,aAAa;YAC7B,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa;YACnC,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,aAAa;YACrC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC;SAC3F,CAAC;IACJ,CAAC;IAGM,SAAS;QAKd,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE/D,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,MAAM,CAAC;QAChB,CAAC;QAGD,IAAI,MAAM,CAAC,GAAG,CAAC,aAAa,GAAG,EAAE,EAAE,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,UAAmB;gBACzB,OAAO,EAAE,mBAAmB,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;gBAClE,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,MAAM,CAAC,GAAG,CAAC,aAAa,GAAG,EAAE,EAAE,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,SAAkB;gBACxB,OAAO,EAAE,uBAAuB,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;gBACtE,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,MAAM,CAAC,MAAM,CAAC,aAAa,GAAG,EAAE,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,UAAmB;gBACzB,OAAO,EAAE,sBAAsB,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;gBACxE,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,MAAM,CAAC,MAAM,CAAC,aAAa,GAAG,EAAE,EAAE,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,SAAkB;gBACxB,OAAO,EAAE,0BAA0B,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;gBAC5E,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,MAAM,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,EAAE,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,UAAmB;gBACzB,OAAO,EAAE,wBAAwB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;gBAC9E,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,MAAM,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,EAAE,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,SAAkB;gBACxB,OAAO,EAAE,mBAAmB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;gBACzE,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAnSD,sCAmSC"}