import { EventEmitter } from 'events';
interface SystemStats {
    timestamp: string;
    uptime: number;
    memory: {
        total: number;
        used: number;
        free: number;
        usage_percent: number;
        cached: number;
        buffers: number;
        available: number;
        swap_total: number;
        swap_used: number;
        swap_free: number;
    };
    cpu: {
        usage_percent: number;
        cores: number;
        load_avg: number[];
        user_time: number;
        system_time: number;
        idle_time: number;
    };
    processes: {
        total: number;
        running: number;
        sleeping: number;
        stopped: number;
        zombie: number;
    };
    network: {
        interfaces: number;
        connections: number;
        bytes_rx: number;
        bytes_tx: number;
        packets_rx: number;
        packets_tx: number;
        errors_rx: number;
        errors_tx: number;
    };
    filesystem: {
        total_space: number;
        used_space: number;
        free_space: number;
        usage_percent: number;
        inodes_total: number;
        inodes_used: number;
        inodes_free: number;
    };
}
export declare class SystemMonitor extends EventEmitter {
    private running;
    private monitorInterval;
    private statsHistory;
    private maxHistorySize;
    private updateInterval;
    private simulationState;
    constructor();
    start(): Promise<void>;
    stop(): Promise<void>;
    isRunning(): boolean;
    getSystemStats(): Promise<SystemStats>;
    getStatsHistory(): SystemStats[];
    private collectStats;
    getCpuHistory(points?: number): Array<{
        timestamp: string;
        value: number;
    }>;
    getMemoryHistory(points?: number): Array<{
        timestamp: string;
        value: number;
    }>;
    getNetworkHistory(points?: number): Array<{
        timestamp: string;
        bytes_rx: number;
        bytes_tx: number;
    }>;
    getCurrentLoad(): {
        cpu: number;
        memory: number;
        disk: number;
        network: number;
    };
    getAlerts(): Array<{
        type: 'warning' | 'error' | 'critical';
        message: string;
        timestamp: string;
    }>;
}
export {};
//# sourceMappingURL=system-monitor.d.ts.map