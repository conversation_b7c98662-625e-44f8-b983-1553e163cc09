"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BridgeServer = void 0;
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const winston_1 = __importDefault(require("winston"));
const uuid_1 = require("uuid");
const kernel_connector_1 = require("./kernel-connector");
const command_processor_1 = require("./command-processor");
const system_monitor_1 = require("./system-monitor");
const logger = winston_1.default.createLogger({
    level: 'info',
    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json()),
    defaultMeta: { service: 'bridge' },
    transports: [
        new winston_1.default.transports.File({ filename: 'error.log', level: 'error' }),
        new winston_1.default.transports.File({ filename: 'combined.log' }),
        new winston_1.default.transports.Console({
            format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple())
        })
    ],
});
class BridgeServer {
    constructor(port = 8080) {
        this.sessions = new Map();
        this.port = port;
        this.app = (0, express_1.default)();
        this.server = (0, http_1.createServer)(this.app);
        this.io = new socket_io_1.Server(this.server, {
            cors: {
                origin: ["http://localhost:3000", "http://localhost:3001"],
                methods: ["GET", "POST"],
                credentials: true
            },
            transports: ['websocket', 'polling']
        });
        this.kernelConnector = new kernel_connector_1.KernelConnector();
        this.commandProcessor = new command_processor_1.CommandProcessor(this.kernelConnector);
        this.systemMonitor = new system_monitor_1.SystemMonitor();
        this.setupMiddleware();
        this.setupRoutes();
        this.setupSocketHandlers();
    }
    setupMiddleware() {
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: false,
        }));
        this.app.use((0, compression_1.default)());
        this.app.use((0, cors_1.default)({
            origin: ["http://localhost:3000", "http://localhost:3001"],
            credentials: true
        }));
        this.app.use(express_1.default.json());
        this.app.use(express_1.default.urlencoded({ extended: true }));
        this.app.use((req, res, next) => {
            logger.info(`${req.method} ${req.path}`, {
                ip: req.ip,
                userAgent: req.get('User-Agent')
            });
            next();
        });
    }
    setupRoutes() {
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                version: '1.0.0',
                services: {
                    kernel: this.kernelConnector.isConnected(),
                    websocket: true,
                    monitor: this.systemMonitor.isRunning()
                }
            });
        });
        this.app.get('/api/stats', async (req, res) => {
            try {
                const stats = await this.systemMonitor.getSystemStats();
                res.json(stats);
            }
            catch (error) {
                logger.error('Failed to get system stats', error);
                res.status(500).json({ error: 'Failed to get system stats' });
            }
        });
        this.app.get('/api/processes', async (req, res) => {
            try {
                const processes = await this.kernelConnector.getProcesses();
                res.json(processes);
            }
            catch (error) {
                logger.error('Failed to get processes', error);
                res.status(500).json({ error: 'Failed to get processes' });
            }
        });
        this.app.get('/api/files', async (req, res) => {
            try {
                const path = req.query.path || '/';
                const files = await this.kernelConnector.listFiles(path);
                res.json(files);
            }
            catch (error) {
                logger.error('Failed to list files', error);
                res.status(500).json({ error: 'Failed to list files' });
            }
        });
        this.app.get('/api/network', async (req, res) => {
            try {
                const networkInfo = await this.kernelConnector.getNetworkInfo();
                res.json(networkInfo);
            }
            catch (error) {
                logger.error('Failed to get network info', error);
                res.status(500).json({ error: 'Failed to get network info' });
            }
        });
        this.app.use('*', (req, res) => {
            res.status(404).json({ error: 'Route not found' });
        });
    }
    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            logger.info(`Client connected: ${socket.id}`);
            const session = {
                id: (0, uuid_1.v4)(),
                socketId: socket.id,
                userId: 'user',
                connectedAt: new Date(),
                lastActivity: new Date(),
                currentDirectory: '/home/<USER>',
                environment: {
                    USER: 'user',
                    HOME: '/home/<USER>',
                    PATH: '/bin:/usr/bin',
                    SHELL: '/bin/bash',
                    TERM: 'xterm-256color'
                }
            };
            this.sessions.set(socket.id, session);
            socket.emit('output', '\x1b[32m✓ Connected to Distributed Kernel Bridge\x1b[0m\r\n');
            socket.emit('output', '\x1b[36mSession ID: ' + session.id + '\x1b[0m\r\n');
            socket.on('command', async (data) => {
                try {
                    session.lastActivity = new Date();
                    logger.info(`Command received from ${socket.id}: ${data.command}`);
                    const result = await this.commandProcessor.processCommand(data.command, session);
                    socket.emit('command_result', result);
                }
                catch (error) {
                    logger.error('Command processing error', error);
                    socket.emit('command_result', {
                        success: false,
                        error: 'Command processing failed',
                        output: null
                    });
                }
            });
            socket.on('file_read', async (data) => {
                try {
                    const content = await this.kernelConnector.readFile(data.path);
                    socket.emit('file_content', { path: data.path, content });
                }
                catch (error) {
                    socket.emit('file_error', { path: data.path, error: error.message });
                }
            });
            socket.on('file_write', async (data) => {
                try {
                    await this.kernelConnector.writeFile(data.path, data.content);
                    socket.emit('file_saved', { path: data.path });
                }
                catch (error) {
                    socket.emit('file_error', { path: data.path, error: error.message });
                }
            });
            socket.on('process_kill', async (data) => {
                try {
                    await this.kernelConnector.killProcess(data.pid);
                    socket.emit('process_killed', { pid: data.pid });
                }
                catch (error) {
                    socket.emit('process_error', { pid: data.pid, error: error.message });
                }
            });
            socket.on('process_create', async (data) => {
                try {
                    const process = await this.kernelConnector.createProcess(data.name, data.priority);
                    socket.emit('process_created', process);
                }
                catch (error) {
                    socket.emit('process_error', { error: error.message });
                }
            });
            socket.on('subscribe_stats', () => {
                logger.info(`Client ${socket.id} subscribed to system stats`);
                const interval = setInterval(async () => {
                    if (!socket.connected) {
                        clearInterval(interval);
                        return;
                    }
                    try {
                        const stats = await this.systemMonitor.getSystemStats();
                        socket.emit('stats_update', stats);
                    }
                    catch (error) {
                        logger.error('Failed to send stats update', error);
                    }
                }, 2000);
                socket.on('disconnect', () => {
                    clearInterval(interval);
                });
            });
            socket.on('disconnect', (reason) => {
                logger.info(`Client disconnected: ${socket.id}, reason: ${reason}`);
                this.sessions.delete(socket.id);
            });
            socket.on('error', (error) => {
                logger.error(`Socket error for ${socket.id}:`, error);
            });
        });
    }
    async start() {
        try {
            await this.kernelConnector.connect();
            logger.info('Connected to kernel');
            await this.systemMonitor.start();
            logger.info('System monitor started');
            this.server.listen(this.port, () => {
                logger.info(`Bridge server running on port ${this.port}`);
                logger.info(`WebSocket endpoint: ws://localhost:${this.port}`);
                logger.info(`HTTP API endpoint: http://localhost:${this.port}`);
            });
            process.on('SIGTERM', () => this.shutdown());
            process.on('SIGINT', () => this.shutdown());
        }
        catch (error) {
            logger.error('Failed to start bridge server', error);
            process.exit(1);
        }
    }
    async shutdown() {
        logger.info('Shutting down bridge server...');
        try {
            this.io.close();
            await this.kernelConnector.disconnect();
            await this.systemMonitor.stop();
            this.server.close(() => {
                logger.info('Bridge server shut down successfully');
                process.exit(0);
            });
        }
        catch (error) {
            logger.error('Error during shutdown', error);
            process.exit(1);
        }
    }
    getSessionCount() {
        return this.sessions.size;
    }
    getSessions() {
        return Array.from(this.sessions.values());
    }
}
exports.BridgeServer = BridgeServer;
if (require.main === module) {
    const port = parseInt(process.env.PORT || '8080');
    const server = new BridgeServer(port);
    server.start().catch((error) => {
        logger.error('Failed to start server', error);
        process.exit(1);
    });
}
//# sourceMappingURL=server.js.map