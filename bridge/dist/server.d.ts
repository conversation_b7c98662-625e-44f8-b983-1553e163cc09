interface ClientSession {
    id: string;
    socketId: string;
    userId: string;
    connectedAt: Date;
    lastActivity: Date;
    currentDirectory: string;
    environment: Record<string, string>;
}
declare class BridgeServer {
    private app;
    private server;
    private io;
    private kernelConnector;
    private commandProcessor;
    private systemMonitor;
    private sessions;
    private port;
    constructor(port?: number);
    private setupMiddleware;
    private setupRoutes;
    private setupSocketHandlers;
    start(): Promise<void>;
    private shutdown;
    getSessionCount(): number;
    getSessions(): ClientSession[];
}
export { BridgeServer, ClientSession };
//# sourceMappingURL=server.d.ts.map