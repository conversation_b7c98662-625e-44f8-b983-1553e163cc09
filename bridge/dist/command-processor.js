"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommandProcessor = void 0;
const winston_1 = __importDefault(require("winston"));
const logger = winston_1.default.createLogger({
    level: 'info',
    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.json()),
    defaultMeta: { service: 'command-processor' },
    transports: [
        new winston_1.default.transports.Console({
            format: winston_1.default.format.simple()
        })
    ],
});
class CommandProcessor {
    constructor(kernelConnector) {
        this.commandHistory = new Map();
        this.kernelConnector = kernelConnector;
    }
    async processCommand(command, session) {
        const trimmedCommand = command.trim();
        if (!trimmedCommand) {
            return { success: true, output: '' };
        }
        const history = this.commandHistory.get(session.id) || [];
        history.push(trimmedCommand);
        if (history.length > 100) {
            history.shift();
        }
        this.commandHistory.set(session.id, history);
        const [cmd, ...args] = trimmedCommand.split(/\s+/);
        try {
            switch (cmd.toLowerCase()) {
                case 'ps':
                    return await this.handlePs(args);
                case 'ls':
                    return await this.handleLs(args, session);
                case 'cat':
                    return await this.handleCat(args);
                case 'echo':
                    return await this.handleEcho(args);
                case 'pwd':
                    return await this.handlePwd(session);
                case 'cd':
                    return await this.handleCd(args, session);
                case 'mkdir':
                    return await this.handleMkdir(args, session);
                case 'touch':
                    return await this.handleTouch(args, session);
                case 'rm':
                    return await this.handleRm(args, session);
                case 'top':
                    return await this.handleTop();
                case 'netstat':
                    return await this.handleNetstat();
                case 'history':
                    return await this.handleHistory(session);
                case 'env':
                    return await this.handleEnv(session);
                case 'export':
                    return await this.handleExport(args, session);
                case 'kill':
                    return await this.handleKill(args);
                case 'jobs':
                    return await this.handleJobs();
                case 'df':
                    return await this.handleDf();
                case 'free':
                    return await this.handleFree();
                case 'uptime':
                    return await this.handleUptime();
                default:
                    return {
                        success: false,
                        error: `Command not found: ${cmd}. Type 'help' for available commands.`
                    };
            }
        }
        catch (error) {
            logger.error(`Command execution error: ${error.message}`);
            return {
                success: false,
                error: `Command execution failed: ${error.message}`
            };
        }
    }
    async handlePs(args) {
        const processes = await this.kernelConnector.getProcesses();
        let output = 'PID\tNAME\t\tSTATE\t\tCPU%\tMEM(MB)\tCREATED\n';
        output += '---\t----\t\t-----\t\t----\t------\t-------\n';
        for (const process of processes) {
            const created = new Date(process.created_at).toLocaleTimeString();
            output += `${process.id}\t${process.name.padEnd(12)}\t${process.state.padEnd(8)}\t${process.cpu_percent.toFixed(1)}\t${process.memory_mb}\t${created}\n`;
        }
        return { success: true, output };
    }
    async handleLs(args, session) {
        const path = args[0] || session.currentDirectory;
        const files = await this.kernelConnector.listFiles(path);
        let output = '';
        for (const file of files) {
            const permissions = file.permissions;
            const size = file.is_dir ? 'DIR' : file.size.toString();
            const modified = new Date(file.modified_at).toLocaleDateString();
            output += `${permissions}\t${file.owner}\t${file.group}\t${size.padStart(8)}\t${modified}\t${file.name}\n`;
        }
        return { success: true, output: output || 'Directory is empty' };
    }
    async handleCat(args) {
        if (args.length === 0) {
            return { success: false, error: 'Usage: cat <filename>' };
        }
        try {
            const content = await this.kernelConnector.readFile(args[0]);
            return { success: true, output: content };
        }
        catch (error) {
            return { success: false, error: error.message };
        }
    }
    async handleEcho(args) {
        const output = args.join(' ');
        return { success: true, output };
    }
    async handlePwd(session) {
        return { success: true, output: session.currentDirectory };
    }
    async handleCd(args, session) {
        const newPath = args[0] || session.environment.HOME || '/home/<USER>';
        if (newPath === '..') {
            const parts = session.currentDirectory.split('/').filter(p => p);
            parts.pop();
            session.currentDirectory = '/' + parts.join('/');
            if (session.currentDirectory === '/')
                session.currentDirectory = '/';
        }
        else if (newPath.startsWith('/')) {
            session.currentDirectory = newPath;
        }
        else {
            session.currentDirectory = session.currentDirectory === '/'
                ? `/${newPath}`
                : `${session.currentDirectory}/${newPath}`;
        }
        return { success: true, output: '' };
    }
    async handleMkdir(args, session) {
        if (args.length === 0) {
            return { success: false, error: 'Usage: mkdir <directory>' };
        }
        return { success: true, output: `Directory created: ${args[0]}` };
    }
    async handleTouch(args, session) {
        if (args.length === 0) {
            return { success: false, error: 'Usage: touch <filename>' };
        }
        try {
            await this.kernelConnector.writeFile(args[0], '');
            return { success: true, output: `File created: ${args[0]}` };
        }
        catch (error) {
            return { success: false, error: error.message };
        }
    }
    async handleRm(args, session) {
        if (args.length === 0) {
            return { success: false, error: 'Usage: rm <filename>' };
        }
        return { success: true, output: `File removed: ${args[0]}` };
    }
    async handleTop() {
        const processes = await this.kernelConnector.getProcesses();
        let output = 'System Monitor - Top Processes\n';
        output += '================================\n\n';
        output += 'PID\tNAME\t\tCPU%\tMEM(MB)\tSTATE\n';
        output += '---\t----\t\t----\t------\t-----\n';
        processes.sort((a, b) => b.cpu_percent - a.cpu_percent);
        for (const process of processes.slice(0, 10)) {
            output += `${process.id}\t${process.name.padEnd(12)}\t${process.cpu_percent.toFixed(1)}\t${process.memory_mb}\t${process.state}\n`;
        }
        return { success: true, output };
    }
    async handleNetstat() {
        const interfaces = await this.kernelConnector.getNetworkInfo();
        let output = 'Network Interfaces\n';
        output += '==================\n\n';
        for (const iface of interfaces) {
            output += `Interface: ${iface.name}\n`;
            output += `  IP Address: ${iface.ip_address}\n`;
            output += `  Netmask: ${iface.netmask}\n`;
            output += `  Gateway: ${iface.gateway || 'N/A'}\n`;
            output += `  MTU: ${iface.mtu}\n`;
            output += `  RX: ${iface.bytes_rx} bytes, ${iface.packets_rx} packets\n`;
            output += `  TX: ${iface.bytes_tx} bytes, ${iface.packets_tx} packets\n\n`;
        }
        return { success: true, output };
    }
    async handleHistory(session) {
        const history = this.commandHistory.get(session.id) || [];
        let output = '';
        history.forEach((cmd, index) => {
            output += `${(index + 1).toString().padStart(4)}: ${cmd}\n`;
        });
        return { success: true, output: output || 'No command history' };
    }
    async handleEnv(session) {
        let output = '';
        for (const [key, value] of Object.entries(session.environment)) {
            output += `${key}=${value}\n`;
        }
        return { success: true, output };
    }
    async handleExport(args, session) {
        if (args.length === 0) {
            return this.handleEnv(session);
        }
        const assignment = args[0];
        const [key, value] = assignment.split('=', 2);
        if (!value) {
            return { success: false, error: 'Usage: export KEY=value' };
        }
        session.environment[key] = value;
        return { success: true, output: '' };
    }
    async handleKill(args) {
        if (args.length === 0) {
            return { success: false, error: 'Usage: kill <pid>' };
        }
        try {
            await this.kernelConnector.killProcess(args[0]);
            return { success: true, output: `Process ${args[0]} terminated` };
        }
        catch (error) {
            return { success: false, error: error.message };
        }
    }
    async handleJobs() {
        return { success: true, output: 'No background jobs' };
    }
    async handleDf() {
        const output = `Filesystem     1K-blocks    Used Available Use% Mounted on
/dev/vfs1        1048576  524288    524288  50% /
tmpfs             262144   16384    245760   7% /tmp
devfs                  0       0         0   0% /dev`;
        return { success: true, output };
    }
    async handleFree() {
        const output = `              total        used        free      shared  buff/cache   available
Mem:        1048576      524288      524288           0           0      524288
Swap:             0           0           0`;
        return { success: true, output };
    }
    async handleUptime() {
        const uptime = process.uptime();
        const hours = Math.floor(uptime / 3600);
        const minutes = Math.floor((uptime % 3600) / 60);
        const output = `up ${hours}:${minutes.toString().padStart(2, '0')}, 1 user, load average: 0.15, 0.12, 0.08`;
        return { success: true, output };
    }
}
exports.CommandProcessor = CommandProcessor;
//# sourceMappingURL=command-processor.js.map