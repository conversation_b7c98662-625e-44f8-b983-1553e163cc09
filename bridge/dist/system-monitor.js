"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemMonitor = void 0;
const events_1 = require("events");
const winston_1 = __importDefault(require("winston"));
const logger = winston_1.default.createLogger({
    level: 'info',
    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.json()),
    defaultMeta: { service: 'system-monitor' },
    transports: [
        new winston_1.default.transports.Console({
            format: winston_1.default.format.simple()
        })
    ],
});
class SystemMonitor extends events_1.EventEmitter {
    constructor() {
        super();
        this.running = false;
        this.monitorInterval = null;
        this.statsHistory = [];
        this.maxHistorySize = 100;
        this.updateInterval = 2000;
        this.simulationState = {
            baseMemoryUsage: 0.4,
            baseCpuUsage: 0.2,
            networkBytesRx: 0,
            networkBytesTx: 0,
            networkPacketsRx: 0,
            networkPacketsTx: 0,
            processCount: 25,
            startTime: Date.now(),
        };
    }
    async start() {
        if (this.running) {
            logger.warn('System monitor is already running');
            return;
        }
        this.running = true;
        logger.info('Starting system monitor');
        this.monitorInterval = setInterval(() => {
            this.collectStats();
        }, this.updateInterval);
        this.collectStats();
        this.emit('started');
    }
    async stop() {
        if (!this.running) {
            return;
        }
        this.running = false;
        logger.info('Stopping system monitor');
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
        }
        this.emit('stopped');
    }
    isRunning() {
        return this.running;
    }
    async getSystemStats() {
        if (this.statsHistory.length === 0) {
            this.collectStats();
        }
        return this.statsHistory[this.statsHistory.length - 1];
    }
    getStatsHistory() {
        return [...this.statsHistory];
    }
    collectStats() {
        const now = new Date();
        const uptime = (Date.now() - this.simulationState.startTime) / 1000;
        const memoryUsage = this.simulationState.baseMemoryUsage +
            (Math.sin(Date.now() / 30000) * 0.1) +
            (Math.random() - 0.5) * 0.05;
        const cpuUsage = this.simulationState.baseCpuUsage +
            (Math.sin(Date.now() / 20000) * 0.3) +
            (Math.random() - 0.5) * 0.1;
        this.simulationState.networkBytesRx += Math.floor(Math.random() * 10000);
        this.simulationState.networkBytesTx += Math.floor(Math.random() * 8000);
        this.simulationState.networkPacketsRx += Math.floor(Math.random() * 100);
        this.simulationState.networkPacketsTx += Math.floor(Math.random() * 80);
        this.simulationState.processCount += Math.floor((Math.random() - 0.5) * 3);
        this.simulationState.processCount = Math.max(15, Math.min(50, this.simulationState.processCount));
        const totalMemory = 1024 * 1024 * 1024;
        const usedMemory = Math.floor(totalMemory * Math.max(0.1, Math.min(0.9, memoryUsage)));
        const freeMemory = totalMemory - usedMemory;
        const totalDisk = 10 * 1024 * 1024 * 1024;
        const usedDisk = Math.floor(totalDisk * 0.3);
        const freeDisk = totalDisk - usedDisk;
        const stats = {
            timestamp: now.toISOString(),
            uptime: Math.floor(uptime),
            memory: {
                total: totalMemory,
                used: usedMemory,
                free: freeMemory,
                usage_percent: (usedMemory / totalMemory) * 100,
                cached: Math.floor(totalMemory * 0.1),
                buffers: Math.floor(totalMemory * 0.05),
                available: freeMemory + Math.floor(totalMemory * 0.15),
                swap_total: 512 * 1024 * 1024,
                swap_used: 0,
                swap_free: 512 * 1024 * 1024,
            },
            cpu: {
                usage_percent: Math.max(0, Math.min(100, cpuUsage * 100)),
                cores: 4,
                load_avg: [
                    Math.max(0, Math.min(4, Math.random() * 2)),
                    Math.max(0, Math.min(4, Math.random() * 1.8)),
                    Math.max(0, Math.min(4, Math.random() * 1.5)),
                ],
                user_time: Math.floor(uptime * 0.6),
                system_time: Math.floor(uptime * 0.2),
                idle_time: Math.floor(uptime * 0.2),
            },
            processes: {
                total: this.simulationState.processCount,
                running: Math.floor(this.simulationState.processCount * 0.1),
                sleeping: Math.floor(this.simulationState.processCount * 0.8),
                stopped: Math.floor(this.simulationState.processCount * 0.05),
                zombie: Math.floor(this.simulationState.processCount * 0.05),
            },
            network: {
                interfaces: 2,
                connections: Math.floor(Math.random() * 20) + 5,
                bytes_rx: this.simulationState.networkBytesRx,
                bytes_tx: this.simulationState.networkBytesTx,
                packets_rx: this.simulationState.networkPacketsRx,
                packets_tx: this.simulationState.networkPacketsTx,
                errors_rx: Math.floor(Math.random() * 3),
                errors_tx: Math.floor(Math.random() * 2),
            },
            filesystem: {
                total_space: totalDisk,
                used_space: usedDisk,
                free_space: freeDisk,
                usage_percent: (usedDisk / totalDisk) * 100,
                inodes_total: 655360,
                inodes_used: Math.floor(655360 * 0.1),
                inodes_free: Math.floor(655360 * 0.9),
            },
        };
        this.statsHistory.push(stats);
        if (this.statsHistory.length > this.maxHistorySize) {
            this.statsHistory.shift();
        }
        this.emit('stats', stats);
        logger.debug('Collected system stats', {
            cpu: stats.cpu.usage_percent.toFixed(1),
            memory: stats.memory.usage_percent.toFixed(1),
            processes: stats.processes.total,
        });
    }
    getCpuHistory(points = 20) {
        return this.statsHistory
            .slice(-points)
            .map(stat => ({
            timestamp: stat.timestamp,
            value: stat.cpu.usage_percent,
        }));
    }
    getMemoryHistory(points = 20) {
        return this.statsHistory
            .slice(-points)
            .map(stat => ({
            timestamp: stat.timestamp,
            value: stat.memory.usage_percent,
        }));
    }
    getNetworkHistory(points = 20) {
        return this.statsHistory
            .slice(-points)
            .map(stat => ({
            timestamp: stat.timestamp,
            bytes_rx: stat.network.bytes_rx,
            bytes_tx: stat.network.bytes_tx,
        }));
    }
    getCurrentLoad() {
        const latest = this.statsHistory[this.statsHistory.length - 1];
        if (!latest) {
            return { cpu: 0, memory: 0, disk: 0, network: 0 };
        }
        return {
            cpu: latest.cpu.usage_percent,
            memory: latest.memory.usage_percent,
            disk: latest.filesystem.usage_percent,
            network: Math.min(100, (latest.network.bytes_rx + latest.network.bytes_tx) / 1000000 * 10),
        };
    }
    getAlerts() {
        const alerts = [];
        const latest = this.statsHistory[this.statsHistory.length - 1];
        if (!latest) {
            return alerts;
        }
        if (latest.cpu.usage_percent > 90) {
            alerts.push({
                type: 'critical',
                message: `High CPU usage: ${latest.cpu.usage_percent.toFixed(1)}%`,
                timestamp: latest.timestamp,
            });
        }
        else if (latest.cpu.usage_percent > 75) {
            alerts.push({
                type: 'warning',
                message: `Elevated CPU usage: ${latest.cpu.usage_percent.toFixed(1)}%`,
                timestamp: latest.timestamp,
            });
        }
        if (latest.memory.usage_percent > 90) {
            alerts.push({
                type: 'critical',
                message: `High memory usage: ${latest.memory.usage_percent.toFixed(1)}%`,
                timestamp: latest.timestamp,
            });
        }
        else if (latest.memory.usage_percent > 80) {
            alerts.push({
                type: 'warning',
                message: `Elevated memory usage: ${latest.memory.usage_percent.toFixed(1)}%`,
                timestamp: latest.timestamp,
            });
        }
        if (latest.filesystem.usage_percent > 95) {
            alerts.push({
                type: 'critical',
                message: `Disk space critical: ${latest.filesystem.usage_percent.toFixed(1)}%`,
                timestamp: latest.timestamp,
            });
        }
        else if (latest.filesystem.usage_percent > 85) {
            alerts.push({
                type: 'warning',
                message: `Low disk space: ${latest.filesystem.usage_percent.toFixed(1)}%`,
                timestamp: latest.timestamp,
            });
        }
        return alerts;
    }
}
exports.SystemMonitor = SystemMonitor;
//# sourceMappingURL=system-monitor.js.map