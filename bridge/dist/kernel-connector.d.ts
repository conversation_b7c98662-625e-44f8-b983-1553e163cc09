import { EventEmitter } from 'events';
interface Process {
    id: string;
    name: string;
    state: string;
    priority: string;
    cpu_percent: number;
    memory_mb: number;
    created_at: string;
    parent_id?: string;
}
interface FileInfo {
    name: string;
    path: string;
    size: number;
    is_dir: boolean;
    permissions: string;
    owner: string;
    group: string;
    modified_at: string;
}
interface NetworkInterface {
    name: string;
    ip_address: string;
    netmask: string;
    gateway: string;
    mtu: number;
    bytes_rx: number;
    bytes_tx: number;
    packets_rx: number;
    packets_tx: number;
}
export declare class KernelConnector extends EventEmitter {
    private connected;
    private reconnectAttempts;
    private maxReconnectAttempts;
    private reconnectDelay;
    constructor();
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    isConnected(): boolean;
    private handleReconnect;
    private startHeartbeat;
    getProcesses(): Promise<Process[]>;
    createProcess(name: string, priority: string): Promise<Process>;
    killProcess(pid: string): Promise<void>;
    listFiles(path: string): Promise<FileInfo[]>;
    readFile(path: string): Promise<string>;
    writeFile(path: string, content: string): Promise<void>;
    getNetworkInfo(): Promise<NetworkInterface[]>;
    syscall(syscallNumber: number, args: any[]): Promise<any>;
}
export {};
//# sourceMappingURL=kernel-connector.d.ts.map