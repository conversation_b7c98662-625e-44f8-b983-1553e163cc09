{"version": 3, "file": "command-processor.js", "sourceRoot": "", "sources": ["../src/command-processor.ts"], "names": [], "mappings": ";;;;;;AAEA,sDAA8B;AAE9B,MAAM,MAAM,GAAG,iBAAO,CAAC,YAAY,CAAC;IAClC,KAAK,EAAE,MAAM;IACb,MAAM,EAAE,iBAAO,CAAC,MAAM,CAAC,OAAO,CAC5B,iBAAO,CAAC,MAAM,CAAC,SAAS,EAAE,EAC1B,iBAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CACtB;IACD,WAAW,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE;IAC7C,UAAU,EAAE;QACV,IAAI,iBAAO,CAAC,UAAU,CAAC,OAAO,CAAC;YAC7B,MAAM,EAAE,iBAAO,CAAC,MAAM,CAAC,MAAM,EAAE;SAChC,CAAC;KACH;CACF,CAAC,CAAC;AAQH,MAAa,gBAAgB;IAI3B,YAAY,eAAgC;QAFpC,mBAAc,GAA0B,IAAI,GAAG,EAAE,CAAC;QAGxD,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IACzC,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,OAAsB;QACjE,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACtC,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;QACvC,CAAC;QAGD,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;QAC1D,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7B,IAAI,OAAO,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACzB,OAAO,CAAC,KAAK,EAAE,CAAC;QAClB,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAE7C,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAEnD,IAAI,CAAC;YACH,QAAQ,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC1B,KAAK,IAAI;oBACP,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAEnC,KAAK,IAAI;oBACP,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAE5C,KAAK,KAAK;oBACR,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAEpC,KAAK,MAAM;oBACT,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAErC,KAAK,KAAK;oBACR,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAEvC,KAAK,IAAI;oBACP,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAE5C,KAAK,OAAO;oBACV,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAE/C,KAAK,OAAO;oBACV,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAE/C,KAAK,IAAI;oBACP,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAE5C,KAAK,KAAK;oBACR,OAAO,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;gBAEhC,KAAK,SAAS;oBACZ,OAAO,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;gBAEpC,KAAK,SAAS;oBACZ,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBAE3C,KAAK,KAAK;oBACR,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAEvC,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAEhD,KAAK,MAAM;oBACT,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAErC,KAAK,MAAM;oBACT,OAAO,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;gBAEjC,KAAK,IAAI;oBACP,OAAO,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAE/B,KAAK,MAAM;oBACT,OAAO,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;gBAEjC,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;gBAEnC;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,sBAAsB,GAAG,uCAAuC;qBACxE,CAAC;YACN,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,6BAA6B,KAAK,CAAC,OAAO,EAAE;aACpD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,IAAc;QACnC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;QAE5D,IAAI,MAAM,GAAG,gDAAgD,CAAC;QAC9D,MAAM,IAAI,+CAA+C,CAAC;QAE1D,KAAK,MAAM,OAAO,IAAI,SAAS,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,kBAAkB,EAAE,CAAC;YAClE,MAAM,IAAI,GAAG,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,SAAS,KAAK,OAAO,IAAI,CAAC;QAC3J,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,IAAc,EAAE,OAAsB;QAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,gBAAgB,CAAC;QACjD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAEzD,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YACrC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACxD,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,kBAAkB,EAAE,CAAC;YAEjE,MAAM,IAAI,GAAG,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC;QAC7G,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnE,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,IAAc;QACpC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,IAAc;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,OAAsB;QAC5C,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC;IAC7D,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,IAAc,EAAE,OAAsB;QAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,YAAY,CAAC;QAGpE,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YACrB,MAAM,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjE,KAAK,CAAC,GAAG,EAAE,CAAC;YACZ,OAAO,CAAC,gBAAgB,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjD,IAAI,OAAO,CAAC,gBAAgB,KAAK,GAAG;gBAAE,OAAO,CAAC,gBAAgB,GAAG,GAAG,CAAC;QACvE,CAAC;aAAM,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACnC,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,KAAK,GAAG;gBACzD,CAAC,CAAC,IAAI,OAAO,EAAE;gBACf,CAAC,CAAC,GAAG,OAAO,CAAC,gBAAgB,IAAI,OAAO,EAAE,CAAC;QAC/C,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;IACvC,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,IAAc,EAAE,OAAsB;QAC9D,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC;QAC/D,CAAC;QAGD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,sBAAsB,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACpE,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,IAAc,EAAE,OAAsB;QAC9D,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,iBAAiB,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,IAAc,EAAE,OAAsB;QAC3D,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC;QAC3D,CAAC;QAGD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,iBAAiB,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC/D,CAAC;IAEO,KAAK,CAAC,SAAS;QACrB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;QAE5D,IAAI,MAAM,GAAG,kCAAkC,CAAC;QAChD,MAAM,IAAI,sCAAsC,CAAC;QACjD,MAAM,IAAI,qCAAqC,CAAC;QAChD,MAAM,IAAI,oCAAoC,CAAC;QAG/C,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC;QAExD,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;YAC7C,MAAM,IAAI,GAAG,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,SAAS,KAAK,OAAO,CAAC,KAAK,IAAI,CAAC;QACrI,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,CAAC;QAE/D,IAAI,MAAM,GAAG,sBAAsB,CAAC;QACpC,MAAM,IAAI,wBAAwB,CAAC;QAEnC,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE,CAAC;YAC/B,MAAM,IAAI,cAAc,KAAK,CAAC,IAAI,IAAI,CAAC;YACvC,MAAM,IAAI,iBAAiB,KAAK,CAAC,UAAU,IAAI,CAAC;YAChD,MAAM,IAAI,cAAc,KAAK,CAAC,OAAO,IAAI,CAAC;YAC1C,MAAM,IAAI,cAAc,KAAK,CAAC,OAAO,IAAI,KAAK,IAAI,CAAC;YACnD,MAAM,IAAI,UAAU,KAAK,CAAC,GAAG,IAAI,CAAC;YAClC,MAAM,IAAI,SAAS,KAAK,CAAC,QAAQ,WAAW,KAAK,CAAC,UAAU,YAAY,CAAC;YACzE,MAAM,IAAI,SAAS,KAAK,CAAC,QAAQ,WAAW,KAAK,CAAC,UAAU,cAAc,CAAC;QAC7E,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAsB;QAChD,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;QAC1D,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YAC7B,MAAM,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnE,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,OAAsB;QAC5C,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/D,MAAM,IAAI,GAAG,GAAG,IAAI,KAAK,IAAI,CAAC;QAChC,CAAC;QACD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,IAAc,EAAE,OAAsB;QAC/D,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3B,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAE9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;QAC9D,CAAC;QAED,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACjC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;IACvC,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,IAAc;QACrC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC;QACxD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,IAAI,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU;QACtB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,oBAAoB,EAAE,CAAC;IACzD,CAAC;IAEO,KAAK,CAAC,QAAQ;QACpB,MAAM,MAAM,GAAG;;;qDAGkC,CAAC;QAElD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,UAAU;QACtB,MAAM,MAAM,GAAG;;4CAEyB,CAAC;QAEzC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAEjD,MAAM,MAAM,GAAG,MAAM,KAAK,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,0CAA0C,CAAC;QAE5G,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACnC,CAAC;CACF;AA5TD,4CA4TC"}